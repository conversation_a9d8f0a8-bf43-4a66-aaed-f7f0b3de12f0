/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Primary Brand Colors - Updated to New Theme (Dark Green)
        primary: {
          50: '#f0f4f1',
          100: '#dce6df',
          200: '#b9cdc0',
          300: '#8fad97',
          400: '#5c8268',
          500: '#2D4D31', // Main brand color - button background
          600: '#1F3322', // Darker shade for hover states
          700: '#1A2B1D',
          800: '#152218',
          900: '#101A13',
          950: '#0A110C',
        },

        // Secondary Colors - Updated to New Theme (Orange/Link Color)
        secondary: {
          50: '#fef6f2',
          100: '#fdeae0',
          200: '#fad1c1',
          300: '#f6b094',
          400: '#f08565',
          500: '#D36C3C', // Main link color
          600: '#B85A32', // Darker shade for hover states
          700: '#9A4A2A',
          800: '#7C3B22',
          900: '#5E2D1A',
          950: '#3F1E11',
        },

        // Accent Colors - Updated to New Theme (Cream/Background Tones)
        accent: {
          50: '#FFFFFF',     // Pure white for alternative sections
          100: '#FFF5EA',    // Main background color
          200: '#FFEDE0',    // Slightly darker cream
          300: '#FFE4D6',    // Medium cream
          400: '#FFDACC',    // Darker cream
          500: '#FFD1C2',    // Even darker cream
          600: '#E6BCA8',    // Muted cream
          700: '#CCA78E',    // Darker muted cream
          800: '#B39274',    // Brown-cream
          900: '#997D5A',    // Dark brown-cream
          950: '#806840',    // Very dark brown-cream
        },

        // Semantic Colors
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
          950: '#052e16',
        },

        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
          950: '#451a03',
        },

        error: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
          950: '#450a0a',
        },

        // Neutral Colors - Enhanced Gray Scale
        neutral: {
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#e5e5e5',
          300: '#d4d4d4',
          400: '#a3a3a3',
          500: '#737373',
          600: '#525252',
          700: '#404040',
          800: '#262626',
          900: '#171717',
          950: '#0a0a0a',
        },

        // New Theme Colors - Updated for consistent branding
        'theme-main-bg': '#FFF5EA',        // Main Background
        'theme-alt-bg': '#FFFFFF',         // Alternative Section Background
        'theme-footer-bg': '#FFFFFF',      // Footer Background
        'theme-primary-text': '#000000',   // Primary Text
        'theme-link': '#D36C3C',          // Link Text
        'theme-button-bg': '#2D4D31',     // Button Background
        'theme-button-text': '#FFFFFF',   // Button Text

        // Legacy color mappings for backward compatibility - Updated to new theme
        primary: '#2D4D31',               // Updated to button color for consistency
        link: '#D36C3C',                  // Updated to new link color
        'link-hover': '#B85A32',          // Darker shade of link color
        highlight: '#2D4D31',             // Updated to button color
        button: '#2D4D31',                // Updated to new button color
        'button-hover': '#1F3322',        // Darker shade of button color
        'button-border': '#2D4D31',       // Updated to button color
        background: '#FFF5EA',            // Updated to main background
        header: '#FFFFFF',                // Keep white for header
        'agri-cream': '#FFF5EA',          // Updated to main background
        'agri-green': '#2D4D31',          // Updated to button color
      },

      // Enhanced Typography
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        display: ['Inter', 'system-ui', 'sans-serif'],
      },

      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1' }],
        '6xl': ['3.75rem', { lineHeight: '1' }],
        '7xl': ['4.5rem', { lineHeight: '1' }],
        '8xl': ['6rem', { lineHeight: '1' }],
        '9xl': ['8rem', { lineHeight: '1' }],
      },

      // Enhanced Spacing
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },

      // Enhanced Animations
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'float-delayed': 'float 6s ease-in-out infinite 2s',
        'pulse-soft': 'pulse-soft 3s ease-in-out infinite',
        'fade-in': 'fade-in 0.6s ease-out',
        'slide-up': 'slide-up 0.6s ease-out',
        'scale-in': 'scale-in 0.4s ease-out',
      },

      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-8px)' },
        },
        'pulse-soft': {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.8' },
        },
        'fade-in': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        'slide-up': {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        'scale-in': {
          '0%': { opacity: '0', transform: 'scale(0.95)' },
          '100%': { opacity: '1', transform: 'scale(1)' },
        },
      },

      // Enhanced Shadows
      boxShadow: {
        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
        'medium': '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'strong': '0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 2px 10px -2px rgba(0, 0, 0, 0.05)',
        'glow-green': '0 0 20px rgba(45, 77, 49, 0.3)',
        'glow-orange': '0 0 20px rgba(211, 108, 60, 0.3)',
      },

      // Enhanced Border Radius
      borderRadius: {
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
      },
    },
  },
  plugins: [],
}
