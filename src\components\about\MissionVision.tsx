import { Eye, Target } from "lucide-react";

const MissionVision = () => {
    {/* Mission & Vision */}
    return (
        <div className="my-20 max-w-[1200px] mx-auto" >
          <div className="grid lg:grid-cols-2 gap-12">
            <div className="bg-[#FFF5EA] p-8 rounded-2xl">
              <div className="flex items-center mb-6">
                <div className="bg-[#2D4D31] p-3 rounded-lg mr-4">
                  <Target className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-[#000000]">Our Mission</h3>
              </div>
              <p className="text-lg text-[#000000] leading-relaxed">
                To democratize agricultural finance by providing a transparent, secure, and 
                efficient blockchain-based platform that empowers farmers, ensures fair trade, 
                and builds trust across the entire supply chain.
              </p>
            </div>

            <div className="bg-[#FFFFFF] p-8 rounded-2xl">
              <div className="flex items-center mb-6">
                <div className="bg-[#D36C3C] p-3 rounded-lg mr-4">
                  <Eye className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-[#000000]">Our Vision</h3>
              </div>
              <p className="text-lg text-[#000000] leading-relaxed">
                To become the leading blockchain platform for agricultural supply chain 
                management, creating a world where every transaction is transparent, 
                every farmer is empowered, and food security is guaranteed.
              </p>
            </div>
          </div>
        </div>
      )
  };
  
  export default MissionVision;