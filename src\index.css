@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }

  html {
    scroll-behavior: smooth;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }

  body {
    line-height: 1.6;
    font-weight: 400;
    color: #000000; /* Updated to theme primary text color */
    background-color: #FFF5EA; /* Updated to theme main background */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Enhanced Typography */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    color: #000000; /* Updated to theme primary text color */
  }

  h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    letter-spacing: -0.02em;
  }

  h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    letter-spacing: -0.01em;
  }

  h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
  }

  p {
    color: #000000; /* Updated to theme primary text color */
    line-height: 1.7;
  }

  /* Focus styles for accessibility */
  *:focus-visible {
    outline: 2px solid #2D4D31; /* Updated to theme button color */
    outline-offset: 2px;
    border-radius: 4px;
  }
}

/* Enhanced Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #2D4D31, #1F3322);
  border-radius: 3px;
  transition: all 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #1F3322, #1A2B1D);
  box-shadow: 0 0 8px rgba(45, 77, 49, 0.3);
}

/* Enhanced Custom Animations */
@keyframes pulse-once {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 8px rgba(45, 77, 49, 0.4);
  }
  50% {
    box-shadow: 0 0 25px rgba(45, 77, 49, 0.7), 0 0 35px rgba(45, 77, 49, 0.3);
  }
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.02);
    opacity: 1;
  }
}

.animate-pulse-once {
  animation: pulse-once 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

.animate-slide-in {
  animation: slideInFromLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-breathe {
  animation: breathe 4s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  background-size: 200% 100%;
  animation: shimmer 2.5s infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-scale-in {
  animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-in-right {
  animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Utility Classes */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .gradient-text {
    background: linear-gradient(135deg, #2D4D31, #D36C3C);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .gradient-text-green {
    background: linear-gradient(135deg, #2D4D31, #1F3322);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .shadow-glow-green {
    box-shadow: 0 0 20px rgba(45, 77, 49, 0.3);
  }

  .shadow-glow-orange {
    box-shadow: 0 0 20px rgba(211, 108, 60, 0.3);
  }
}

/* Component Styles */
@layer components {
  .btn-primary {
    @apply bg-[#2D4D31] hover:bg-[#1F3322] text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 shadow-medium hover:shadow-glow-green border-2 border-[#2D4D31] hover:border-[#1F3322];
  }

  .btn-secondary {
    @apply bg-white text-[#2D4D31] border-2 border-[#2D4D31] hover:bg-[#2D4D31] hover:text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 shadow-soft hover:shadow-medium;
  }

  .btn-outline {
    @apply bg-transparent text-[#2D4D31] border-2 border-[#2D4D31] hover:bg-[#2D4D31] hover:text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300;
  }

  .card {
    @apply bg-white rounded-2xl shadow-soft hover:shadow-medium transition-all duration-300 border border-neutral-200;
  }

  .card-elevated {
    @apply bg-white rounded-2xl shadow-medium hover:shadow-strong transition-all duration-300 border border-neutral-200;
  }
}