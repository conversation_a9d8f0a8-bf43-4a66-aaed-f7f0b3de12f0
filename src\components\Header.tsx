import { useState, useEffect } from 'react';
import {  Menu, X } from 'lucide-react';
import logo from '../assets/logo.png';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { label: 'Home', href: '#home' },
    {label: 'Coming Soon', href: '#coming-soon'},
    { label: 'About', href: '#about' },
    { label: 'Features', href: '#features' },
    { label: 'White Paper', href: '#whitepaper' },
    { label: 'Video', href: '#video' },
    { label: 'Contact', href: '#contact' },
  ];

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMenuOpen(false);
  };

  return (
    <header className={`sticky top-0 z-50 backdrop-blur-md transition-all duration-300
      ${isScrolled ? 'bg-[#FFFFFF]/75 shadow-lg' : 'bg-[#FFFFFF]/30'}
    `}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 w-full">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <img src={logo} alt="Agritram Logo" className="h-10 w-auto" />
          </div>

          {/* Right side: Navigation + CTA */}
          <div className="flex items-center ml-auto space-x-7">
            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-7">
              {navItems.map((item) => (
                <button
                  key={item.label}
                  onClick={() => scrollToSection(item.href)}
                  className="text-[#000000] hover:text-[#D36C3C] transition-colors duration-200 font-medium"
                >
                  {item.label}
                </button>
              ))}
            </nav>

            {/* CTA Buttons */}
            <div className="hidden md:flex items-center space-x-4">
              <button
                onClick={() => scrollToSection('#partner')}
                className="text-[#000000] hover:text-[#D36C3C] font-medium transition-colors duration-200 px-4 py-2 rounded-lg hover:bg-[#FFF5EA]"
              >
                Partner With Us
              </button>
              <button
                onClick={() => scrollToSection('#early-access')}
                className="bg-[#2D4D31] hover:bg-[#1F3322] text-[#FFFFFF] border border-dotted border-[#2D4D31] border-opacity-70 font-medium transition-colors duration-200 px-4 py-2 rounded-lg"
              >
                Get Early Access
              </button>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 rounded-md text-[#000000] hover:text-[#D36C3C] hover:bg-[#FFF5EA] transition-colors duration-200"
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden absolute top-16 left-0 right-0 bg-[#FFFFFF] shadow-lg border-t">
            <div className="px-4 py-4 space-y-4">
              {navItems.map((item) => (
                <button
                  key={item.label}
                  onClick={() => scrollToSection(item.href)}
                  className="block w-full text-left text-[#000000] hover:text-[#D36C3C] hover:bg-[#FFF5EA] transition-colors duration-200 font-medium py-2 px-2 rounded-lg"
                >
                  {item.label}
                </button>
              ))}
              <div className="border-t pt-4 space-y-2">
                <button
                  onClick={() => scrollToSection('#partner')}
                  className="block w-full text-[#000000] hover:text-[#D36C3C] hover:bg-[#FFF5EA] transition-colors duration-200 font-medium py-2 rounded-lg text-center"
                >
                  Partner With Us
                </button>
                <button
                  onClick={() => scrollToSection('#early-access')}
                  className="block w-full bg-[#2D4D31] text-[#FFFFFF] border border-dotted border-[#2D4D31] border-opacity-70 hover:bg-[#1F3322] font-medium py-2 rounded-lg text-center"
                >
                  Get Early Access
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;