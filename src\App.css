/* Enhanced Hero Section Styles */
.hero__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(45, 77, 49, 0.1) 0%,
    rgba(45, 77, 49, 0.05) 50%,
    rgba(211, 108, 60, 0.05) 100%
  );
  z-index: -1;
}

.hero__background {
  background: linear-gradient(
    135deg,
    #FFF5EA 0%,
    #FFFFFF 50%,
    #FFF5EA 100%
  );
}

.hero__floating-element {
  position: absolute;
  border-radius: 50%;
  opacity: 0.6;
  animation: float 8s ease-in-out infinite;
  background: linear-gradient(135deg, rgba(45, 77, 49, 0.8), rgba(31, 51, 34, 0.6));
  box-shadow: 0 4px 20px rgba(45, 77, 49, 0.3);
}

.hero__floating-element:nth-child(2n) {
  animation-delay: -2s;
  background: linear-gradient(135deg, rgba(211, 108, 60, 0.8), rgba(184, 90, 50, 0.6));
  box-shadow: 0 4px 20px rgba(211, 108, 60, 0.3);
}

.hero__floating-element:nth-child(3n) {
  animation-delay: -4s;
  background: linear-gradient(135deg, rgba(45, 77, 49, 0.7), rgba(31, 51, 34, 0.5));
  box-shadow: 0 4px 20px rgba(45, 77, 49, 0.3);
}

/* Enhanced About Section Styles */
.about-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.story-content {
  background: linear-gradient(135deg, #ffffff 0%, #FFF5EA 100%);
  border-radius: 1.5rem;
  padding: 3rem;
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(45, 77, 49, 0.1);
  position: relative;
  overflow: hidden;
}

.story-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #2D4D31, #D36C3C, #2D4D31);
  border-radius: 1.5rem 1.5rem 0 0;
}

.story-content h3 {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 700;
  color: #000000;
  margin-bottom: 2rem;
  text-align: center;
  letter-spacing: -0.01em;
  line-height: 1.2;
}

.story-content p {
  font-size: 1.125rem;
  color: #000000;
  line-height: 1.8;
  margin-bottom: 1.5rem;
  text-align: justify;
}

.story-content p:last-child {
  margin-bottom: 0;
}

/* Enhanced Card Styles */
.feature-card {
  background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  border: 1px solid #e5e7eb;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 2px 10px -2px rgba(0, 0, 0, 0.05);
  border-color: rgba(45, 77, 49, 0.3);
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #2D4D31, #D36C3C);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.feature-card:hover::before {
  transform: scaleX(1);
}

/* Timeline Styles */
.timeline-item {
  position: relative;
  padding-left: 2rem;
  margin-bottom: 2rem;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #2D4D31;
  box-shadow: 0 0 0 4px rgba(45, 77, 49, 0.2);
}

.timeline-item.completed::before {
  background: #22c55e;
  box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.2);
}

.timeline-item.current::before {
  background: #D36C3C;
  box-shadow: 0 0 0 4px rgba(211, 108, 60, 0.2);
  animation: pulse 2s infinite;
}

/* Button Enhancement */
.btn-enhanced {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #2D4D31, #1F3322);
  border: none;
  border-radius: 0.75rem;
  color: white;
  font-weight: 600;
  padding: 0.875rem 2rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(45, 77, 49, 0.3);
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-enhanced:hover::before {
  left: 100%;
}

.btn-enhanced:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(45, 77, 49, 0.4);
}